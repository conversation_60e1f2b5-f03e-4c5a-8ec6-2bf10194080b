export function getRandomLocation(location, dimension, baseOffset, additionalOffset, randomYOffset, checkForAirBlock) {
    const randomOffset = () => {
        const totalOffset = baseOffset + additionalOffset;
        return Math.random() * (totalOffset * 2) - totalOffset;
    };
    const generateRandomLocation = () => ({
        x: location.x + randomOffset(),
        y: location.y + randomYOffset,
        z: location.z + randomOffset()
    });
    const isAirBlock = (location) => {
        const block = dimension.getBlock(location);
        return block?.isAir ?? false;
    };
    let randomLocation = generateRandomLocation();
    if (!checkForAirBlock) {
        return randomLocation;
    }
    let attempts = 0;
    while (!isAirBlock(randomLocation) && attempts < 30) {
        randomLocation = generateRandomLocation();
        attempts++;
    }
    return attempts < 30 ? randomLocation : undefined;
}
export function getDistance(point1, point2) {
    const dx = point1.x - point2.x;
    const dy = point1.y - point2.y;
    const dz = point1.z - point2.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
}
export function getDirection(from, to) {
    const dx = to.x - from.x;
    const dy = to.y - from.y;
    const dz = to.z - from.z;
    const length = Math.sqrt(dx * dx + dy * dy + dz * dz);
    if (length > 0) {
        return {
            x: dx / length,
            y: dy / length,
            z: dz / length
        };
    }
    else {
        return { x: 0, y: 0, z: 0 };
    }
}
export function createVector3(x, y, z) {
    return { x, y, z };
}
