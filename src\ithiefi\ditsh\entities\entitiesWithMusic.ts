import { Entity, Player, system } from "@minecraft/server";

export const entitiesWithMusic = new Map<string, string>([["ditsh:ao_oni", "mob.ditsh.ao_oni.chase"]]);

// Play music for this entity
export function playMusicForEntity(entity: Entity, music: string) {
  const nearbyPlayers = getNearbyPlayers(entity, 256);

  for (const player of nearbyPlayers) {
    const isCurrentlyPlaying = isPlayerPlayingMusic(player, entity.typeId);

    if (!isCurrentlyPlaying) {
      player.runCommand(`playsound ${music} @s ~ ~ ~ 1.0 1.0`);
      player.setDynamicProperty(`${entity.typeId}_music`, true);
    }
  }
  return;
}

// Stop playing music for this entity
export function stopMusicForEntity(entity: Entity, music: string) {
    const nearbyPlayers = getNearbyPlayers(entity, 256);

    for (const player of nearbyPlayers) {
      const isCurrentlyPlaying = isPlayerPlayingMusic(player, entity.typeId);

      if (isCurrentlyPlaying) {
        player.runCommand(`stopsound @s ${music}`);
        player.setDynamicProperty(`${entity.typeId}_music`, false);
      }
    }
  return;
}

// Continue playing music when the world/entity gets reloaded.
// For entities with music
export async function continueMusicForEntity(entity: Entity, music: string) {
  const playMusic = entity.getProperty("ditsh:playing_music") as boolean;

  if (playMusic) {
    await system.waitTicks(140);
    const nearbyPlayers = getNearbyPlayers(entity, 256);

    for (const player of nearbyPlayers) {
      const isCurrentlyPlaying = isPlayerPlayingMusic(player, entity.typeId);

      if (!isCurrentlyPlaying) {
        player.runCommand(`playsound ${music} @s ~ ~ ~ 1.0 1.0`);
        player.setDynamicProperty(`${entity.typeId}_music`, true);
      }
    }
  }
  return;
}

// Function to call when a player rejoin the world
export function resetPlayerMusic(player: Player) {
  for (const [entityTypeId, music] of entitiesWithMusic) {
    const isCurrentlyPlaying = isPlayerPlayingMusic(player, entityTypeId);

    if (isCurrentlyPlaying) {
      player.runCommand(`stopsound @s ${music}`);
      player.setDynamicProperty(`${entityTypeId}_music`, false);
    }
  }
  return;
}

// Helper function to get nearby players within a certain range
function getNearbyPlayers(entity: Entity, range: number): Player[] {
  const players = entity.dimension.getPlayers({
    location: entity.location,
    maxDistance: range
  });
  return players;
}

// Helper function to check if a player is already playing music for a specific entity
function isPlayerPlayingMusic(player: Player, entityTypeId: string): boolean {
  const playingMusic = player.getDynamicProperty(`${entityTypeId}_music`) as boolean;
  return playingMusic;
}
