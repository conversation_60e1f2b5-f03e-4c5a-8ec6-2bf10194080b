import { Player, world } from "@minecraft/server";
import { setEntityToCardinalDirection } from "../utilities/rotation";
import { entitiesWithMusic, continueMusicForEntity, stopMusicForEntity, playMusicForEntity, resetPlayerMusic } from "./entitiesWithMusic";

export function initEntityListeners() {
    // General entity spawn event listener
    world.afterEvents.entitySpawn.subscribe((data) => {
        const entity = data.entity;
        const typeId = entity.typeId;
        if (typeId === "ditsh:door1" || typeId === "ditsh:door2") {
            setEntityToCardinalDirection(entity);
        }
    });

    // General entity load event listener
    world.afterEvents.entityLoad.subscribe((data) => {
        const entity = data.entity;
        const typeId = entity.typeId;
        if (entitiesWithMusic.has(typeId)) {
            continueMusicForEntity(entity, entitiesWithMusic.get(typeId)!);
        }
        else if (entity instanceof Player) {
            resetPlayerMusic(entity);
        }
    });

    // General data driven entity event listener
    world.afterEvents.dataDrivenEntityTrigger.subscribe((data) => {
        const entity = data.entity;
        const typeId = entity.typeId;
        const eventId = data.eventId;
        if (eventId === "ditsh:start_chase_music" || eventId === "ditsh:maintain_chase_music") {
            playMusicForEntity(entity, entitiesWithMusic.get(typeId)!);
        }
        else if (eventId === "ditsh:stop_chase_music" || eventId === "ditsh:on_death") {
            stopMusicForEntity(entity, entitiesWithMusic.get(typeId)!);
        }
    });
}