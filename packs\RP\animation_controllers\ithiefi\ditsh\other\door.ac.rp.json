{"format_version": "1.19.0", "animation_controllers": {"controller.animation.ithiefi_ditsh_door.general": {"initial_state": "close", "states": {"close": {"animations": ["close"], "transitions": [{"open": "q.property('ditsh:state') == 'open'"}], "blend_transition": 0.1, "blend_via_shortest_path": true}, "open": {"animations": ["open"], "transitions": [{"close": "q.property('ditsh:state') == 'close'"}], "blend_transition": 0.1, "blend_via_shortest_path": true}}}}}