{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:door1", "is_spawnable": true, "is_summonable": true, "properties": {"ditsh:state": {"client_sync": true, "type": "enum", "values": ["open", "close"], "default": "close"}}}, "component_groups": {"door_closed": {"minecraft:entity_sensor": {"subsensors": [{"event": "ditsh:open_door", "cooldown": 0.1, "range": [2, 1], "minimum_count": 1, "event_filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "ditsh"}]}}]}}, "door_open": {"minecraft:entity_sensor": {"subsensors": [{"event": "ditsh:start_closing", "cooldown": 0.1, "range": [2, 1], "minimum_count": 0, "maximum_count": 0, "event_filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "ditsh"}]}}]}}, "door_closing": {"minecraft:entity_sensor": {"subsensors": [{"event": "ditsh:keep_open", "cooldown": 0.1, "range": [2, 1], "minimum_count": 1, "event_filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "ditsh"}]}}]}, "minecraft:timer": {"time": 2.0, "looping": false, "time_down_event": {"event": "ditsh:close_door"}}}}, "events": {"ditsh:open_door": {"remove": {"component_groups": ["door_closed"]}, "add": {"component_groups": ["door_open"]}, "set_property": {"ditsh:state": "open"}}, "ditsh:start_closing": {"remove": {"component_groups": ["door_open"]}, "add": {"component_groups": ["door_closing"]}}, "ditsh:close_door": {"remove": {"component_groups": ["door_closing"]}, "add": {"component_groups": ["door_closed"]}, "set_property": {"ditsh:state": "close"}}, "ditsh:keep_open": {"remove": {"component_groups": ["door_closing"]}, "add": {"component_groups": ["door_open"]}}}, "components": {"minecraft:type_family": {"family": ["inanimate", "door"]}, "minecraft:entity_sensor": {"subsensors": [{"event": "ditsh:open_door", "cooldown": 0.1, "range": [2, 1], "minimum_count": 1, "event_filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "ditsh"}]}}]}, "minecraft:collision_box": {"width": 1, "height": 3}, "minecraft:persistent": {}, "minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"any_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:wooden_axe"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:stone_axe"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:iron_axe"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:golden_axe"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:diamond_axe"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:netherite_axe"}]}}, "deals_damage": "yes"}, {"on_damage": {"filters": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:wooden_axe", "operator": "not"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:stone_axe", "operator": "not"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:iron_axe", "operator": "not"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:golden_axe", "operator": "not"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:diamond_axe", "operator": "not"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:netherite_axe", "operator": "not"}]}, "deals_damage": "no"}]}, "minecraft:health": {"value": 40, "max": 40}, "minecraft:knockback_resistance": {"value": 1, "max": 1}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}}}}