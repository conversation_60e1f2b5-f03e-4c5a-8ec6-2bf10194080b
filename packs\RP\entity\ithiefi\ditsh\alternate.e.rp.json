{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ditsh:alternate", "min_engine_version": "1.16.0", "materials": {"default": "entity_alphatest"}, "textures": {"default": "textures/ithiefi/ditsh/entities/alternate"}, "geometry": {"default": "geometry.ithiefi_ditsh_alternate"}, "animations": {"look_at_target": "animation.common.look_at_target", "twitch": "animation.ithiefi_ditsh_alternate.twitch", "idle": "animation.ithiefi_ditsh_alternate.idle", "walk": "animation.ithiefi_ditsh_alternate.walk", "attack": "animation.ithiefi_ditsh_alternate.attack", "general": "controller.animation.ithiefi_ditsh_alternate.general"}, "scripts": {"animate": [{"look_at_target": "q.is_alive"}, "twitch", "general"]}, "render_controllers": ["controller.render.default"], "spawn_egg": {"base_color": "#1b1919", "overlay_color": "#c1b5b5"}}}}